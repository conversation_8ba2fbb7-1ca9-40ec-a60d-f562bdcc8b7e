import { useEffect } from "react"

export const usePerformanceMonitor = () => {
  useEffect(() => {
    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'navigation') {
          console.log('Navigation timing:', {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            firstPaint: entry.responseEnd - entry.requestStart
          })
        }
        
        if (entry.entryType === 'paint') {
          console.log(`${entry.name}: ${entry.startTime}ms`)
        }
        
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime)
        }
      })
    })

    // Observe different performance metrics
    try {
      observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint'] })
    } catch (e) {
      // Fallback for browsers that don't support all entry types
      console.log('Performance monitoring not fully supported')
    }

    return () => observer.disconnect()
  }, [])

  // Monitor bundle sizes in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const logBundleInfo = () => {
        const scripts = document.querySelectorAll('script[src]')
        const styles = document.querySelectorAll('link[rel="stylesheet"]')
        
        console.log('Loaded resources:', {
          scripts: scripts.length,
          stylesheets: styles.length
        })
      }

      window.addEventListener('load', logBundleInfo)
      return () => window.removeEventListener('load', logBundleInfo)
    }
  }, [])
}
