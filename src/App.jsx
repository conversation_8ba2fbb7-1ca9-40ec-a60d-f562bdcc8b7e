import { useEffect, Suspense, lazy } from "react"
import { IconContext } from "react-icons"

import { ShootingStars } from "@/components/ui/ShootingStars"
import { StarsBackground } from "@/components/ui/StarsBackground"
import ResourcePreloader from "@/components/ui/ResourcePreloader"
import { initializeColorTheme } from "@/lib/colors"
import { usePerformanceMonitor } from "@/hooks/usePerformanceMonitor"

import Layout from "./components/Layout/Layout"
import AboutMe from "./pages/AboutMe/AboutMe"

// Lazy load non-critical sections to reduce initial bundle size
const Skills = lazy(() => import("./pages/Skills/Skills"))
const AITools = lazy(() => import("@/pages/AITools/AITools"))
const Interests = lazy(() => import("@/pages/Interests/Interests"))
const Experience = lazy(() => import("./pages/Experience/Experience"))
const Contact = lazy(() => import("./pages/Contact/Contact"))

// Loading component for lazy-loaded sections
const SectionLoader = () => (
  <div className="section flex items-center justify-center">
    <div className="flex items-center gap-3">
      <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      <span className="text-sm text-muted-foreground">Loading...</span>
    </div>
  </div>
)

const App = () => {
  // Monitor performance in development
  usePerformanceMonitor()

  // Ensure page always loads at top and initialize color theme
  useEffect(() => {
    // Disable scroll restoration
    if ("scrollRestoration" in history) {
      history.scrollRestoration = "manual"
    }

    // Force scroll to top on load
    window.scrollTo(0, 0)

    // Initialize color theme from saved preference
    initializeColorTheme()
  }, [])

  return (
    <IconContext.Provider value={{ style: { verticalAlign: "middle" } }}>
      <ResourcePreloader />
      <div className="relative min-h-screen">
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 [background:radial-gradient(125%_125%_at_50%_10%,#000_40%,hsl(var(--primary))_100%)]" />
          <ShootingStars />
          <StarsBackground starDensity={0.0008} />
        </div>
        <Layout>
          <div className="relative z-10 pt-4">
            {/* Load AboutMe immediately as it's above the fold */}
            <AboutMe />

            {/* Lazy load other sections with Suspense */}
            <Suspense fallback={<SectionLoader />}>
              <Skills />
            </Suspense>

            <Suspense fallback={<SectionLoader />}>
              <AITools />
            </Suspense>

            <Suspense fallback={<SectionLoader />}>
              <Interests />
            </Suspense>

            <Suspense fallback={<SectionLoader />}>
              <Experience />
            </Suspense>

            <Suspense fallback={<SectionLoader />}>
              <Contact />
            </Suspense>
          </div>
        </Layout>
      </div>
    </IconContext.Provider>
  )
}

export default App
