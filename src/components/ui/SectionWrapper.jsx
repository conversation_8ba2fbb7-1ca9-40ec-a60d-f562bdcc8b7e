import { useState, useRef, useEffect } from "react"
import { motion } from "framer-motion"

const SectionWrapper = ({ 
  children, 
  id, 
  className = "", 
  enableAnimation = true,
  threshold = 0.1,
  ...props 
}) => {
  const [isInView, setIsInView] = useState(false)
  const sectionRef = useRef(null)

  useEffect(() => {
    if (!enableAnimation) {
      setIsInView(true)
      return
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          // Keep observing for exit animations if needed
        } else {
          // Optional: Reset animation when out of view
          // setIsInView(false)
        }
      },
      {
        threshold,
        rootMargin: "0px 0px -10% 0px" // Trigger when 10% from bottom of viewport
      }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [enableAnimation, threshold])

  const animationVariants = {
    hidden: {
      opacity: 0,
      y: 50
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.1
      }
    }
  }

  if (!enableAnimation) {
    return (
      <section
        ref={sectionRef}
        id={id}
        className={`section ${className}`}
        data-section
        {...props}
      >
        {children}
      </section>
    )
  }

  return (
    <motion.section
      ref={sectionRef}
      id={id}
      className={`section ${className}`}
      data-section
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={animationVariants}
      {...props}
    >
      {children}
    </motion.section>
  )
}

export default SectionWrapper
