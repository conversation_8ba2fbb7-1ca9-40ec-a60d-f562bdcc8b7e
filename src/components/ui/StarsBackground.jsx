import { useCallback, useEffect, useRef, useState } from "react"

import { cn } from "@/lib/utils"

export const StarsBackground = ({
  starDensity = 0.00015,
  allStarsTwinkle = true,
  twinkleProbability = 0.7,
  minTwinkleSpeed = 0.5,
  maxTwinkleSpeed = 1,
  className
}) => {
  const [stars, setStars] = useState([])
  const canvasRef = useRef(null)

  const generateStars = useCallback(
    (width, height) => {
      const area = width * height
      const numStars = Math.floor(area * starDensity)
      return Array.from({ length: numStars }, () => {
        const shouldTwinkle =
          allStarsTwinkle || Math.random() < twinkleProbability
        return {
          x: Math.random() * width,
          y: Math.random() * height,
          radius: Math.random() * 0.05 + 0.5,
          opacity: Math.random() * 0.5 + 0.5,
          twinkleSpeed: shouldTwinkle
            ? minTwinkleSpeed +
              Math.random() * (maxTwinkleSpeed - minTwinkleSpeed)
            : null
        }
      })
    },
    [
      starDensity,
      allStarsTwinkle,
      twinkleProbability,
      minTwinkleSpeed,
      maxTwinkleSpeed
    ]
  )

  useEffect(() => {
    const updateStars = () => {
      if (canvasRef.current) {
        const canvas = canvasRef.current
        const ctx = canvas.getContext("2d")
        if (!ctx) return

        const { width, height } = canvas.getBoundingClientRect()
        canvas.width = width
        canvas.height = height
        setStars(generateStars(width, height))
      }
    }

    updateStars()

    const resizeObserver = new ResizeObserver(updateStars)
    if (canvasRef.current) {
      resizeObserver.observe(canvasRef.current)
    }

    return () => {
      if (canvasRef.current) {
        resizeObserver.unobserve(canvasRef.current)
      }
    }
  }, [
    starDensity,
    allStarsTwinkle,
    twinkleProbability,
    minTwinkleSpeed,
    maxTwinkleSpeed,
    generateStars
  ])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let animationFrameId
    let lastTime = 0
    const targetFPS = 30 // Reduce FPS for better performance
    const frameInterval = 1000 / targetFPS

    const render = (currentTime) => {
      if (currentTime - lastTime >= frameInterval) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // Use batch rendering for better performance
        ctx.fillStyle = 'rgba(255, 255, 255, 1)'
        stars.forEach((star) => {
          if (star.twinkleSpeed !== null) {
            star.opacity =
              0.5 +
              Math.abs(Math.sin((currentTime * 0.001) / star.twinkleSpeed) * 0.5)
          }

          ctx.globalAlpha = star.opacity
          ctx.beginPath()
          ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2)
          ctx.fill()
        })

        ctx.globalAlpha = 1
        lastTime = currentTime
      }

      animationFrameId = requestAnimationFrame(render)
    }

    animationFrameId = requestAnimationFrame(render)

    return () => {
      cancelAnimationFrame(animationFrameId)
    }
  }, [stars])

  return (
    <canvas
      ref={canvasRef}
      className={cn("absolute inset-0 h-full w-full", className)}
    />
  )
}
