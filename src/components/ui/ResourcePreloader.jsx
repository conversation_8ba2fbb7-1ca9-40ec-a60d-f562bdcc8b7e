import { useEffect } from "react"

const ResourcePreloader = () => {
  useEffect(() => {
    // Preload critical images that will be needed soon
    const criticalImages = [
      "/src/assets/skills/react.svg",
      "/src/assets/skills/typescript.svg",
      "/src/assets/skills/javascript.svg",
      "/src/assets/skills/python.svg"
    ]

    const preloadImages = () => {
      criticalImages.forEach((src) => {
        const link = document.createElement("link")
        link.rel = "preload"
        link.as = "image"
        link.href = src
        document.head.appendChild(link)
      })
    }

    // Preload after a short delay to not block initial render
    const timer = setTimeout(preloadImages, 100)
    
    return () => clearTimeout(timer)
  }, [])

  return null
}

export default ResourcePreloader
