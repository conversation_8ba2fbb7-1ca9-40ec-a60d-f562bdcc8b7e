import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import LazyImage from "@/components/ui/LazyImage"
import SkillCardSkeleton from "@/components/ui/SkillCardSkeleton"
import SectionWrapper from "@/components/ui/SectionWrapper"

import classNames from "classnames"
import { hexToRgba } from "@/lib/utils"

// Lazy load skill icons to reduce initial bundle size
const skillIcons = {
  django: () => import("@/assets/skills/django.svg"),
  aws: () => import("@/assets/skills/aws.svg"),
  react: () => import("@/assets/skills/react.svg"),
  docker: () => import("@/assets/skills/docker.svg"),
  git: () => import("@/assets/skills/git.svg"),
  javascript: () => import("@/assets/skills/javascript.svg"),
  jira: () => import("@/assets/skills/jira.svg"),
  linux: () => import("@/assets/skills/linux.svg"),
  postgresql: () => import("@/assets/skills/postgresql.svg"),
  postman: () => import("@/assets/skills/postman.svg"),
  python: () => import("@/assets/skills/python.svg"),
  sass: () => import("@/assets/skills/sass.svg"),
  zustand: () => import("@/assets/skills/zustand.svg"),
  tailwindcss: () => import("@/assets/skills/tailwindcss.svg"),
  typescript: () => import("@/assets/skills/typescript.svg"),
  vite: () => import("@/assets/skills/vite.svg"),
  zod: () => import("@/assets/skills/zod.svg"),
  react_query: () => import("@/assets/skills/react_query.svg")
}

const SkillCard = ({ skill, index }) => {
  const [iconSrc, setIconSrc] = useState(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadIcon = async () => {
      try {
        const iconModule = await skillIcons[skill.iconKey]()
        setIconSrc(iconModule.default)
      } catch (error) {
        console.error(`Failed to load icon for ${skill.name}:`, error)
      } finally {
        setIsLoading(false)
      }
    }

    // Stagger icon loading to prevent overwhelming the browser
    const delay = index * 50 // 50ms delay between each icon
    const timer = setTimeout(loadIcon, delay)

    return () => clearTimeout(timer)
  }, [skill.iconKey, skill.name, index])

  if (isLoading) {
    return <SkillCardSkeleton />
  }

  return (
    <Card className="group relative overflow-hidden border-white/10 bg-white/5 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:bg-white/10 hover:shadow-lg hover:shadow-primary/25">
      <CardContent className={classNames("flex flex-col items-center justify-center p-6", skill.wrapperClassName)}>
        <div>
          <LazyImage
            src={iconSrc}
            alt={skill.name}
            className={classNames("mb-3 size-8 md:size-10", skill.className)}
            style={{ color: skill.color }}
          />
        </div>
        <Badge variant="skill" className="text-xs">
          {skill.name}
        </Badge>
      </CardContent>

      <div className="absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
        <div
          className="absolute inset-0 rounded-lg blur-xl"
          style={{
            background: `radial-gradient(circle at center, ${hexToRgba(skill.color, 0.2)} 0%, transparent 70%)`
          }}
        />
      </div>
    </Card>
  )
}

const Skills = () => {
  const skills = [
    { name: "React", iconKey: "react", color: "#61DAFB" },
    { name: "TypeScript", iconKey: "typescript", color: "#3178C6" },
    { name: "JavaScript", iconKey: "javascript", color: "#F7DF1E" },
    { name: "Python", iconKey: "python", color: "#3776AB" },
    { name: "Django", iconKey: "django", color: "#45B78B" },
    { name: "Docker", iconKey: "docker", color: "#2496ED" },
    { name: "PostgreSQL", iconKey: "postgresql", color: "#336791" },
    { name: "AWS", iconKey: "aws", color: "#FF9900" },
    { name: "Git", iconKey: "git", color: "#F05032" },
    { name: "Sass", iconKey: "sass", color: "#CC6699" },
    { name: "Jira", iconKey: "jira", color: "#0052CC" },
    { name: "Linux", iconKey: "linux", color: "#FCC624" },
    { name: "Postman", iconKey: "postman", color: "#FF6C37" },
    {
      name: "Zustand",
      iconKey: "zustand",
      color: "#6E5E6B",
      className: "!size-16 mb-0",
      wrapperClassName: "pb-[24px] pt-3"
    },
    { name: "Tailwind CSS", iconKey: "tailwindcss", color: "#38BDF8" },
    { name: "Vite", iconKey: "vite", color: "#FF00FF" },
    { name: "Zod", iconKey: "zod", color: "#1F5A7A" },
    { name: "React Query", iconKey: "react_query", color: "#FF4154" }
  ]

  return (
    <SectionWrapper id="skills">
      <div className="container mx-auto px-8">
        <div className="mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Skills & Technologies
          </h2>

          <p className="text-sm text-muted-foreground md:text-base">
            Technologies and tools I work with
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4 px-0 md:px-16 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-6">
          {skills.map((skill, index) => (
            <div key={skill.name}>
              <SkillCard skill={skill} index={index} />
            </div>
          ))}
        </div>
      </div>
    </SectionWrapper>
  )
}

export default Skills
