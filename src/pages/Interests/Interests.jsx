import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/assets/interests/neovim.svg"
import TanStack<PERSON>ogo from "@/assets/interests/tanstack.svg"
import BiomeLogo from "@/assets/interests/biome.svg"
import <PERSON><PERSON><PERSON><PERSON> from "@/assets/interests/bun.svg"
import <PERSON><PERSON><PERSON><PERSON> from "@/assets/interests/ghostty.svg"
import <PERSON><PERSON><PERSON> from "@/assets/interests/mcp.svg"
import MillionLogo from "@/assets/interests/million.svg"
import RuffLogo from "@/assets/interests/ruff.svg"
import UvLogo from "@/assets/interests/uv.svg"
import TmuxLogo from "@/assets/interests/tmux.svg"
import Kubernetes<PERSON>ogo from "@/assets/interests/kubernetes.svg"
import Motion<PERSON>ogo from "@/assets/interests/motion.svg"


import classNames from "classnames"
import { hexToRgba } from "@/lib/utils"

const Interests = () => {
  const skills = [
    { name: "<PERSON>V<PERSON>", icon: <PERSON><PERSON><PERSON><PERSON><PERSON>, color: "#57A9FF" },
    { name: "TanStack", icon: TanStack<PERSON>ogo, color: "#FFA400" },
    { name: "Biome", icon: BiomeLogo, color: "#60a5fa" },
    { name: "Bun", icon: BunLogo, color: "#FFFFFF" },
    { name: "Ghostty Terminal", icon: GhosttyLogo, color: "#FFFFFF" },
    { name: "MCP", icon: MCPLogo, color: "#FFFFFF" },
    { name: "Million Lint", icon: MillionLogo, color: "#B073D8" },
    { name: "Ruff", icon: RuffLogo, color: "#fcc21b" },
    { name: "uv", icon: UvLogo, color: "#DE5FE9" },
    { name: "Tmux", icon: TmuxLogo, color: "#1bb91f" },
    { name: "Kubernetes", icon: KubernetesLogo, color: "#326CE5" },
    { name: "Motion", icon: MotionLogo, color: "#FFF312" }
  ]

  return (
    <section id="interests" className="section">
      <div className="container mx-auto px-8">
        <div className="mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Interests
          </h2>

          <p className="text-sm text-muted-foreground md:text-base">
            Technologies and tools I am interested in
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4 px-0 md:grid-cols-3 md:px-16 lg:grid-cols-5 xl:grid-cols-6">
          {skills.map((skill) => {
            return (
              <div key={skill.name}>
                <Card className="group relative overflow-hidden border-white/10 bg-white/5 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:bg-white/10 hover:shadow-lg hover:shadow-primary/25">
                  <CardContent
                    className={classNames(
                      "flex flex-col items-center justify-center p-6",
                      skill.wrapperClassName
                    )}
                  >
                    <div>
                      <img
                        src={skill.icon}
                        alt={skill.name}
                        className={classNames(
                          "mb-3 size-8 md:size-10",
                          skill.className
                        )}
                        style={{ color: skill.color }}
                      />
                    </div>
                    <Badge variant="skill" className="text-xs">
                      {skill.name}
                    </Badge>
                  </CardContent>

                  <div className="absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                    <div
                      className="absolute inset-0 rounded-lg blur-xl"
                      style={{
                        background: `radial-gradient(circle at center, ${hexToRgba(skill.color, 0.2)} 0%, transparent 70%)`
                      }}
                    />
                  </div>
                </Card>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default Interests
