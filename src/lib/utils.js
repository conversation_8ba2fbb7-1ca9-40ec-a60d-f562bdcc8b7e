import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

/**
 * Converts a hex color to rgba format with specified alpha
 * @param {string} hex - Hex color code (e.g., "#61DAFB")
 * @param {number} alpha - Alpha value between 0 and 1 (e.g., 0.2)
 * @returns {string} - RGBA color string (e.g., "rgba(97, 218, 251, 0.2)")
 */
export function hexToRgba(hex, alpha) {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}
